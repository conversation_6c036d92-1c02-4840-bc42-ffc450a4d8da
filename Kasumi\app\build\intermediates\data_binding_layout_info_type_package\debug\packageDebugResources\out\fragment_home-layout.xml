<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="fragment_home" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\fragment_home.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.core.widget.NestedScrollView"><Targets><Target tag="layout/fragment_home_0" view="androidx.core.widget.NestedScrollView"><Expressions/><location startLine="1" startOffset="0" endLine="167" endOffset="39"/></Target><Target id="@+id/welcomeText" view="TextView"><Expressions/><location startLine="37" startOffset="16" endLine="43" endOffset="69"/></Target><Target id="@+id/statusIndicator" view="View"><Expressions/><location startLine="79" startOffset="20" endLine="84" endOffset="57"/></Target><Target id="@+id/statusText" view="TextView"><Expressions/><location startLine="86" startOffset="20" endLine="93" endOffset="73"/></Target><Target id="@+id/btnFloatingWindow" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="139" startOffset="20" endLine="149" endOffset="66"/></Target><Target id="@+id/btnLandscape" view="com.google.android.material.button.MaterialButton"><Expressions/><location startLine="151" startOffset="20" endLine="157" endOffset="61"/></Target></Targets></Layout>