package mc.meson.kasumi

import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import android.util.Log
import androidx.core.view.WindowCompat
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import mc.meson.kasumi.databinding.ActivityMainWithNavigationBinding

class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainWithNavigationBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        try {
            Log.d(TAG, "MainActivity onCreate")

            // Enable edge-to-edge display
            WindowCompat.setDecorFitsSystemWindows(window, false)

            binding = ActivityMainWithNavigationBinding.inflate(layoutInflater)
            setContentView(binding.root)

            setupNavigation()

            Log.d(TAG, "MainActivity created successfully")
        } catch (e: Exception) {
            Log.e(TAG, "Error creating MainActivity", e)
        }
    }

    private fun setupNavigation() {
        // Set up the toolbar
        setSupportActionBar(binding.toolbar)

        // Set up navigation
        val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
        val navController = navHostFragment.navController

        // Connect bottom navigation with navigation controller
        binding.bottomNavigation.setupWithNavController(navController)
    }



    companion object {
        private const val TAG = "MainActivity"
    }
}