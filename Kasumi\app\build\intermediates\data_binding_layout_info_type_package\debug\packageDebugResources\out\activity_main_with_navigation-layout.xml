<?xml version="1.0" encoding="utf-8" standalone="yes"?><Layout layout="activity_main_with_navigation" modulePackage="mc.meson.kasumi" filePath="app\src\main\res\layout\activity_main_with_navigation.xml" directory="layout" isMerge="false" isBindingData="false" rootNodeType="androidx.coordinatorlayout.widget.CoordinatorLayout"><Targets><Target tag="layout/activity_main_with_navigation_0" view="androidx.coordinatorlayout.widget.CoordinatorLayout"><Expressions/><location startLine="1" startOffset="0" endLine="51" endOffset="53"/></Target><Target id="@+id/appBarLayout" view="com.google.android.material.appbar.AppBarLayout"><Expressions/><location startLine="10" startOffset="4" endLine="25" endOffset="53"/></Target><Target id="@+id/toolbar" view="com.google.android.material.appbar.MaterialToolbar"><Expressions/><location startLine="17" startOffset="8" endLine="23" endOffset="62"/></Target><Target id="@+id/nav_host_fragment" view="androidx.fragment.app.FragmentContainerView"><Expressions/><location startLine="28" startOffset="4" endLine="35" endOffset="46"/></Target><Target id="@+id/bottom_navigation" view="com.google.android.material.bottomnavigation.BottomNavigationView"><Expressions/><location startLine="38" startOffset="4" endLine="49" endOffset="62"/></Target></Targets></Layout>